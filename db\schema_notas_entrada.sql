-- Schema para Notas de Entrada - Sistema de Auditoria Fiscal
-- Tabelas separadas para armazenar dados de notas de entrada importadas via SPED

-- Tabela de Clientes de Entrada (Fornecedores) - TAG 0150
CREATE TABLE IF NOT EXISTS cliente_entrada (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cod_part VARCHAR(50) NOT NULL, -- Código do participante no SPED
    cnpj VARCHAR(18), -- CNPJ do fornecedor
    cpf VARCHAR(14), -- CPF para pessoas físicas
    razao_social VARCHAR(255) NOT NULL,
    inscricao_estadual VARCHAR(30),
    codigo_municipio VARCHAR(10),
    suframa VARCHAR(20),
    endereco VARCHAR(255),
    numero VARCHAR(20),
    complemento VARCHAR(255),
    bairro VARCHAR(100),
    codigo_pais VARCHAR(10) DEFAULT '1058', -- Brasil
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (empresa_id, cod_part)
);

-- Tabela de Produtos de Entrada - TAG 0200
CREATE TABLE IF NOT EXISTS produto_entrada (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cod_item VARCHAR(50) NOT NULL, -- Código do item no SPED
    descricao VARCHAR(255) NOT NULL,
    codigo_barra VARCHAR(50),
    codigo_anterior VARCHAR(50),
    unidade VARCHAR(10),
    tipo_item VARCHAR(2), -- 01=Mercadoria, 02=Matéria-prima, etc
    ncm VARCHAR(20),
    ex_ipi VARCHAR(10),
    codigo_genero VARCHAR(10),
    codigo_lst VARCHAR(10),
    aliquota_icms DECIMAL(5,2),
    cest VARCHAR(10),
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (empresa_id, cod_item)
);

-- Tabela de Notas de Entrada - TAG C100
CREATE TABLE IF NOT EXISTS nota_entrada (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_entrada_id INTEGER REFERENCES cliente_entrada(id) NOT NULL,
    ind_oper VARCHAR(1) NOT NULL, -- 0=Entrada, 1=Saída
    ind_emit VARCHAR(1) NOT NULL, -- 0=Emissão própria, 1=Terceiros
    cod_part VARCHAR(50) NOT NULL, -- Código do participante
    cod_mod VARCHAR(2) NOT NULL, -- Código do modelo do documento
    cod_sit VARCHAR(2) NOT NULL, -- Código da situação do documento
    serie VARCHAR(10),
    numero_nf VARCHAR(20) NOT NULL,
    chave_nf VARCHAR(44),
    data_documento DATE NOT NULL,
    data_entrada_saida DATE NOT NULL,
    valor_documento DECIMAL(15,2),
    ind_pgto VARCHAR(1), -- Indicador do tipo de pagamento
    valor_desconto DECIMAL(15,2),
    valor_abatimento DECIMAL(15,2),
    valor_mercadorias DECIMAL(15,2),
    ind_frt VARCHAR(1), -- Indicador do tipo de frete
    valor_frete DECIMAL(15,2),
    valor_seguro DECIMAL(15,2),
    valor_outras_despesas DECIMAL(15,2),
    valor_bc_icms DECIMAL(15,2),
    valor_icms DECIMAL(15,2),
    valor_bc_icms_st DECIMAL(15,2),
    valor_icms_st DECIMAL(15,2),
    valor_ipi DECIMAL(15,2),
    valor_pis DECIMAL(15,2),
    valor_cofins DECIMAL(15,2),
    valor_pis_st DECIMAL(15,2),
    valor_cofins_st DECIMAL(15,2),
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (empresa_id, chave_nf)
);

-- Tabela de Itens de Notas de Entrada - TAG C170
CREATE TABLE IF NOT EXISTS item_nota_entrada (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    nota_entrada_id INTEGER REFERENCES nota_entrada(id) NOT NULL,
    produto_entrada_id INTEGER REFERENCES produto_entrada(id) NOT NULL,
    num_item INTEGER NOT NULL, -- Número sequencial do item
    cod_item VARCHAR(50) NOT NULL, -- Código do item
    descricao_complementar VARCHAR(255),
    quantidade DECIMAL(15,4),
    unidade VARCHAR(10),
    valor_item DECIMAL(15,2),
    valor_desconto DECIMAL(15,2),
    ind_mov VARCHAR(1), -- Indicador de movimentação física
    cst_icms VARCHAR(3),
    cfop VARCHAR(4),
    codigo_natureza VARCHAR(10),
    valor_bc_icms DECIMAL(15,2),
    aliquota_icms DECIMAL(5,2),
    valor_icms DECIMAL(15,2),
    valor_bc_icms_st DECIMAL(15,2),
    aliquota_st DECIMAL(5,2),
    valor_icms_st DECIMAL(15,2),
    ind_apur VARCHAR(1), -- Indicador de apuração do IPI
    cst_ipi VARCHAR(2),
    codigo_enquadramento VARCHAR(10),
    valor_bc_ipi DECIMAL(15,2),
    aliquota_ipi DECIMAL(5,2),
    valor_ipi DECIMAL(15,2),
    cst_pis VARCHAR(2),
    valor_bc_pis DECIMAL(15,2),
    aliquota_pis DECIMAL(5,4),
    quantidade_bc_pis DECIMAL(15,4),
    aliquota_pis_reais DECIMAL(15,4),
    valor_pis DECIMAL(15,2),
    cst_cofins VARCHAR(2),
    valor_bc_cofins DECIMAL(15,2),
    aliquota_cofins DECIMAL(5,4),
    quantidade_bc_cofins DECIMAL(15,4),
    aliquota_cofins_reais DECIMAL(15,4),
    valor_cofins DECIMAL(15,2),
    codigo_conta VARCHAR(50),
    valor_abatimento DECIMAL(15,2),
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela para histórico de importações SPED
CREATE TABLE IF NOT EXISTS importacao_sped (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    usuario_id INTEGER REFERENCES usuario(id) NOT NULL,
    arquivo_nome VARCHAR(255) NOT NULL,
    cnpj_empresa VARCHAR(18),
    razao_social_empresa VARCHAR(255),
    data_inicio DATE, -- DT_INI da TAG 0000
    data_fim DATE, -- DT_FIN da TAG 0000
    total_notas INTEGER DEFAULT 0,
    total_itens INTEGER DEFAULT 0,
    total_clientes INTEGER DEFAULT 0,
    total_produtos INTEGER DEFAULT 0,
    data_importacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'concluido', -- 'concluido', 'erro'
    mensagem TEXT
);

-- Índices para melhorar performance
CREATE INDEX IF NOT EXISTS idx_cliente_entrada_empresa_cod_part ON cliente_entrada(empresa_id, cod_part);
CREATE INDEX IF NOT EXISTS idx_cliente_entrada_cnpj ON cliente_entrada(cnpj);
CREATE INDEX IF NOT EXISTS idx_produto_entrada_empresa_cod_item ON produto_entrada(empresa_id, cod_item);
CREATE INDEX IF NOT EXISTS idx_nota_entrada_empresa_chave ON nota_entrada(empresa_id, chave_nf);
CREATE INDEX IF NOT EXISTS idx_nota_entrada_data ON nota_entrada(data_documento);
CREATE INDEX IF NOT EXISTS idx_item_nota_entrada_nota ON item_nota_entrada(nota_entrada_id);
CREATE INDEX IF NOT EXISTS idx_item_nota_entrada_produto ON item_nota_entrada(produto_entrada_id);
CREATE INDEX IF NOT EXISTS idx_importacao_sped_empresa ON importacao_sped(empresa_id);
CREATE INDEX IF NOT EXISTS idx_importacao_sped_data ON importacao_sped(data_importacao);

-- Comentários nas tabelas
COMMENT ON TABLE cliente_entrada IS 'Fornecedores das notas de entrada (TAG 0150 do SPED)';
COMMENT ON TABLE produto_entrada IS 'Produtos das notas de entrada (TAG 0200 do SPED)';
COMMENT ON TABLE nota_entrada IS 'Notas fiscais de entrada (TAG C100 do SPED)';
COMMENT ON TABLE item_nota_entrada IS 'Itens das notas de entrada (TAG C170 do SPED)';
COMMENT ON TABLE importacao_sped IS 'Histórico de importações de arquivos SPED';

-- Commit das alterações
COMMIT;
