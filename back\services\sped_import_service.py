"""
Service para importação de arquivos SPED
"""

import logging
import traceback
from typing import Dict, List, Optional
from datetime import datetime

from models import (
    db, Empresa, Cliente, ProdutoEntrada, NotaEntrada,
    ItemNotaEntrada, ImportacaoSped
)
from utils.sped_processor import SPEDProcessor
from utils.api_client import fetch_cnpj_data
from utils.sped_tipo_mapper import get_tipo_produto_descricao
from services.cenario_service import CenarioService

logger = logging.getLogger(__name__)


class SPEDImportService:
    """
    Service para importar arquivos SPED e processar notas de entrada
    """

    def __init__(self, empresa_id: int, escritorio_id: int, usuario_id: int, websocket_service=None, import_id=None):
        """
        Inicializa o service de importação SPED

        Args:
            empresa_id (int): ID da empresa
            escritorio_id (int): ID do escritório
            usuario_id (int): ID do usuário
            websocket_service: Serviço WebSocket para notificações
            import_id: ID único da importação
        """
        self.empresa_id = empresa_id
        self.escritorio_id = escritorio_id
        self.usuario_id = usuario_id
        self.websocket_service = websocket_service
        self.import_id = import_id

    def import_sped(self, sped_content: str, filename: str) -> Dict:
        """
        Importa um arquivo SPED
        
        Args:
            sped_content (str): Conteúdo do arquivo SPED
            filename (str): Nome do arquivo
            
        Returns:
            dict: Resultado da importação
        """
        importacao = None
        try:
            # Processar o arquivo SPED
            processor = SPEDProcessor(sped_content)
            
            # Obter dados da empresa do SPED
            empresa_data = processor.get_empresa_data()
            
            # Verificar se a empresa existe e se o CNPJ confere
            empresa = Empresa.query.get(self.empresa_id)
            if not empresa:
                importacao = self._create_error_import_record(
                    filename, 'Empresa não encontrada', empresa_data
                )
                return {
                    'success': False,
                    'message': 'Empresa não encontrada',
                    'importacao': importacao.to_dict() if importacao else None
                }

            # Verificar se o CNPJ da empresa confere com o do SPED
            cnpj_sped = empresa_data.get('cnpj', '')
            if cnpj_sped and empresa.cnpj != cnpj_sped:
                importacao = self._create_error_import_record(
                    filename, 
                    f'CNPJ da empresa ({empresa.cnpj}) não confere com o CNPJ do SPED ({cnpj_sped})',
                    empresa_data
                )
                return {
                    'success': False,
                    'message': f'CNPJ da empresa não confere com o CNPJ do SPED',
                    'importacao': importacao.to_dict() if importacao else None
                }

            # Enviar progresso: Processando participantes
            self._send_progress("Processando fornecedores...", 20)

            # Processar participantes (fornecedores)
            participantes = processor.get_participantes_entrada()
            clientes_processados = self._process_clientes(participantes)

            # Enviar progresso: Processando produtos
            self._send_progress("Processando produtos...", 40)

            # Processar produtos
            produtos = processor.get_produtos_entrada()
            produtos_processados = self._process_produtos(produtos)

            # Enviar progresso: Processando notas
            self._send_progress("Processando notas de entrada...", 60)

            # Processar notas de entrada
            notas_e_itens = processor.get_all_itens_entrada()
            notas_processadas, itens_processados = self._process_notas_entrada(
                notas_e_itens, clientes_processados, produtos_processados
            )

            # Enviar progresso: Finalizando
            self._send_progress("Finalizando importação...", 80)

            # Criar registro de importação com sucesso
            importacao = ImportacaoSped(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                usuario_id=self.usuario_id,
                arquivo_nome=filename,
                cnpj_empresa=empresa_data.get('cnpj'),
                razao_social_empresa=empresa_data.get('nome'),
                data_inicio=empresa_data.get('dt_ini'),
                data_fim=empresa_data.get('dt_fim'),
                total_notas=len(notas_processadas),
                total_itens=len(itens_processados),
                total_clientes=len(clientes_processados),
                total_produtos=len(produtos_processados),
                status='concluido'
            )

            db.session.add(importacao)
            db.session.commit()

            # Criar cenários automaticamente (em transação separada)
            try:
                self._create_cenarios_automaticos(itens_processados)
            except Exception as e:
                logger.warning(f"Erro ao criar cenários automaticamente: {str(e)}")
                # Não falhar a importação por causa dos cenários

            return {
                'success': True,
                'message': 'SPED importado com sucesso',
                'importacao': importacao.to_dict(),
                'totais': {
                    'notas': len(notas_processadas),
                    'itens': len(itens_processados),
                    'clientes': len(clientes_processados),
                    'produtos': len(produtos_processados)
                }
            }

        except Exception as e:
            db.session.rollback()
            error_msg = str(e)
            stack_trace = traceback.format_exc()

            logger.error(f"Erro ao importar SPED {filename}: {error_msg}")
            logger.error(f"Stack trace: {stack_trace}")

            # Criar registro de importação com erro
            if not importacao:
                importacao = self._create_error_import_record(filename, error_msg)

            return {
                'success': False,
                'message': f'Erro ao importar SPED: {error_msg}',
                'error': error_msg,
                'stack_trace': stack_trace,
                'importacao': importacao.to_dict() if importacao else None
            }

    def _process_clientes(self, participantes: Dict) -> List[Cliente]:
        """
        Processa e salva os clientes (fornecedores) de entrada
        """
        clientes_processados = []

        for cod_part, dados in participantes.items():
            try:
                # Obter CNPJ do fornecedor
                cnpj = dados.get('cnpj')
                cpf = dados.get('cpf')

                # Verificar se o cliente já existe (primeiro por CNPJ/CPF, depois por cod_part)
                cliente_existente = None
                if cnpj:
                    cliente_existente = Cliente.query.filter_by(
                        empresa_id=self.empresa_id,
                        cnpj=cnpj
                    ).first()
                elif cpf:
                    cliente_existente = Cliente.query.filter_by(
                        empresa_id=self.empresa_id,
                        cpf=cpf
                    ).first()

                # Se não encontrou por CNPJ/CPF, buscar por cod_part
                if not cliente_existente:
                    cliente_existente = Cliente.query.filter_by(
                        empresa_id=self.empresa_id,
                        cod_part=cod_part
                    ).first()

                if cliente_existente:
                    # Atualizar cod_part se não estiver definido
                    if not cliente_existente.cod_part:
                        cliente_existente.cod_part = cod_part
                        db.session.flush()
                    clientes_processados.append(cliente_existente)
                    continue

                # Variáveis para dados da API
                cnae = None
                atividade = None
                destinacao = None
                simples_nacional = False
                natureza_juridica = None

                # Se for um novo cliente com CNPJ (não CPF), buscar dados adicionais na API
                if cnpj and not cpf:
                    logger.info(f"Buscando dados do CNPJ {cnpj} na API para fornecedor {cod_part}")
                    try:
                        # Buscar dados do CNPJ na API
                        api_data = fetch_cnpj_data(cnpj)

                        if api_data:
                            # Extrair CNAE, atividade, destinação e status do Simples Nacional
                            cnae = api_data.get('cnae')
                            simples_nacional = api_data.get('simples_nacional', False)

                            # Verificar se a natureza jurídica é 'Produtor Rural' e tem prioridade
                            natureza_juridica_data = api_data.get('natureza_juridica', {})
                            descricao_natureza_juridica = natureza_juridica_data.get('descricao', '').lower() if natureza_juridica_data else ''
                            natureza_juridica = natureza_juridica_data.get('descricao', '') if natureza_juridica_data else ''

                            if 'produtor rural' in descricao_natureza_juridica:
                                # Se a natureza jurídica for 'Produtor Rural', definir a atividade como 'Produtor Rural'
                                # independentemente do CNAE
                                atividade = 'Produtor Rural'
                                logger.info(f"Natureza jurídica identificada como 'Produtor Rural'. Atividade definida como: {atividade}")
                            else:
                                # Se não for 'Produtor Rural', usar atividade e destinação da API
                                atividade = api_data.get('atividade')
                                destinacao = api_data.get('destinacao')
                                logger.info(f"Usando atividade da API: {atividade}, destinação: {destinacao}")

                            logger.info(f"Dados obtidos da API para {cnpj}: CNAE={cnae}, "
                                        f"Atividade={atividade}, Destinação={destinacao}, "
                                        f"Natureza Jurídica={descricao_natureza_juridica}, "
                                        f"Simples Nacional={simples_nacional}")
                        else:
                            logger.warning(f"Não foi possível obter dados do CNPJ {cnpj} na API.")
                    except Exception as e:
                        logger.error(f"Erro ao buscar dados do CNPJ {cnpj} na API: {str(e)}")

                # Criar novo cliente
                cliente = Cliente(
                    empresa_id=self.empresa_id,
                    escritorio_id=self.escritorio_id,
                    cod_part=cod_part,
                    cnpj=cnpj,
                    cpf=cpf,
                    razao_social=dados.get('nome', ''),
                    inscricao_estadual=dados.get('ie'),
                    codigo_municipio=dados.get('cod_mun'),
                    suframa=dados.get('suframa'),
                    endereco=dados.get('endereco'),
                    numero=dados.get('numero'),
                    complemento=dados.get('complemento'),
                    bairro=dados.get('bairro'),
                    # Novos campos da API CNPJ
                    cnae=cnae,
                    atividade=atividade,
                    destinacao=destinacao,
                    simples_nacional=simples_nacional,
                    natureza_juridica=natureza_juridica
                )

                db.session.add(cliente)
                clientes_processados.append(cliente)

            except Exception as e:
                logger.error(f"Erro ao processar cliente {cod_part}: {str(e)}")
                continue

        return clientes_processados

    def _process_produtos(self, produtos: Dict) -> List[ProdutoEntrada]:
        """
        Processa e salva os produtos de entrada
        """
        produtos_processados = []
        
        for cod_item, dados in produtos.items():
            try:
                # Verificar se o produto já existe
                produto_existente = ProdutoEntrada.query.filter_by(
                    empresa_id=self.empresa_id,
                    cod_item=cod_item
                ).first()

                if produto_existente:
                    produtos_processados.append(produto_existente)
                    continue

                # Obter descrição do tipo de produto SPED
                tipo_item_codigo = dados.get('tipo_item')
                tipo_item_descricao = get_tipo_produto_descricao(tipo_item_codigo)

                # Criar novo produto
                produto = ProdutoEntrada(
                    empresa_id=self.empresa_id,
                    escritorio_id=self.escritorio_id,
                    cod_item=cod_item,
                    descricao=dados.get('descr_item', ''),
                    codigo_barra=dados.get('cod_barra'),
                    codigo_anterior=dados.get('cod_ant_item'),
                    unidade=dados.get('unid_inv'),
                    tipo_item=tipo_item_codigo,
                    ncm=dados.get('cod_ncm'),
                    ex_ipi=dados.get('ex_ipi'),
                    codigo_genero=dados.get('cod_gen'),
                    codigo_lst=dados.get('cod_lst'),
                    aliquota_icms=dados.get('aliq_icms'),
                    cest=dados.get('cest')
                )

                logger.info(f"Produto criado: {cod_item} - {dados.get('descr_item', '')} - Tipo: {tipo_item_descricao}")

                db.session.add(produto)
                produtos_processados.append(produto)

            except Exception as e:
                logger.error(f"Erro ao processar produto {cod_item}: {str(e)}")
                continue

        return produtos_processados

    def _process_notas_entrada(self, notas_e_itens: List, clientes_map: List, produtos_map: List) -> tuple:
        """
        Processa e salva as notas de entrada e seus itens
        """
        notas_processadas = []
        itens_processados = []
        
        # Criar mapas para busca rápida
        clientes_dict = {c.cod_part: c for c in clientes_map}
        produtos_dict = {p.cod_item: p for p in produtos_map}
        
        for nota_data, itens_data in notas_e_itens:
            try:
                # Verificar se a nota já existe
                chave_nf = nota_data.get('chv_nfe')
                if chave_nf:
                    nota_existente = NotaEntrada.query.filter_by(
                        empresa_id=self.empresa_id,
                        chave_nf=chave_nf
                    ).first()
                    
                    if nota_existente:
                        continue

                # Buscar cliente
                cod_part = nota_data.get('cod_part')
                cliente = clientes_dict.get(cod_part)
                if not cliente:
                    logger.warning(f"Cliente {cod_part} não encontrado para nota {nota_data.get('num_doc')}")
                    continue

                # Criar nota de entrada
                nota = NotaEntrada(
                    empresa_id=self.empresa_id,
                    escritorio_id=self.escritorio_id,
                    cliente_id=cliente.id,
                    ind_oper=nota_data.get('ind_oper'),
                    ind_emit=nota_data.get('ind_emit'),
                    cod_part=cod_part,
                    cod_mod=nota_data.get('cod_mod'),
                    cod_sit=nota_data.get('cod_sit'),
                    serie=nota_data.get('ser'),
                    numero_nf=nota_data.get('num_doc'),
                    chave_nf=chave_nf,
                    data_documento=nota_data.get('dt_doc'),
                    data_entrada_saida=nota_data.get('dt_e_s'),
                    valor_documento=nota_data.get('vl_doc'),
                    ind_pgto=nota_data.get('ind_pgto'),
                    valor_desconto=nota_data.get('vl_desc'),
                    valor_abatimento=nota_data.get('vl_abat_nt'),
                    valor_mercadorias=nota_data.get('vl_merc'),
                    ind_frt=nota_data.get('ind_frt'),
                    valor_frete=nota_data.get('vl_frt'),
                    valor_seguro=nota_data.get('vl_seg'),
                    valor_outras_despesas=nota_data.get('vl_out_da'),
                    valor_bc_icms=nota_data.get('vl_bc_icms'),
                    valor_icms=nota_data.get('vl_icms'),
                    valor_bc_icms_st=nota_data.get('vl_bc_icms_st'),
                    valor_icms_st=nota_data.get('vl_icms_st'),
                    valor_ipi=nota_data.get('vl_ipi'),
                    valor_pis=nota_data.get('vl_pis'),
                    valor_cofins=nota_data.get('vl_cofins'),
                    valor_pis_st=nota_data.get('vl_pis_st'),
                    valor_cofins_st=nota_data.get('vl_cofins_st')
                )

                db.session.add(nota)
                db.session.flush()  # Para obter o ID da nota
                notas_processadas.append(nota)

                # Processar itens da nota
                for item_data in itens_data:
                    cod_item = item_data.get('cod_item')
                    produto = produtos_dict.get(cod_item)
                    if not produto:
                        logger.warning(f"Produto {cod_item} não encontrado para item da nota {nota.numero_nf}")
                        continue

                    item = ItemNotaEntrada(
                        empresa_id=self.empresa_id,
                        escritorio_id=self.escritorio_id,
                        nota_entrada_id=nota.id,
                        produto_entrada_id=produto.id,
                        num_item=item_data.get('num_item'),
                        cod_item=cod_item,
                        descricao_complementar=item_data.get('descr_compl'),
                        quantidade=item_data.get('qtd'),
                        unidade=item_data.get('unid'),
                        valor_item=item_data.get('vl_item'),
                        valor_desconto=item_data.get('vl_desc'),
                        ind_mov=item_data.get('ind_mov'),
                        cst_icms=item_data.get('cst_icms'),
                        cfop=item_data.get('cfop'),
                        codigo_natureza=item_data.get('cod_nat'),
                        valor_bc_icms=item_data.get('vl_bc_icms'),
                        aliquota_icms=item_data.get('aliq_icms'),
                        valor_icms=item_data.get('vl_icms'),
                        valor_bc_icms_st=item_data.get('vl_bc_icms_st'),
                        aliquota_st=item_data.get('aliq_st'),
                        valor_icms_st=item_data.get('vl_icms_st'),
                        ind_apur=item_data.get('ind_apur'),
                        cst_ipi=item_data.get('cst_ipi'),
                        codigo_enquadramento=item_data.get('cod_enq'),
                        valor_bc_ipi=item_data.get('vl_bc_ipi'),
                        aliquota_ipi=item_data.get('aliq_ipi'),
                        valor_ipi=item_data.get('vl_ipi'),
                        cst_pis=item_data.get('cst_pis'),
                        valor_bc_pis=item_data.get('vl_bc_pis'),
                        aliquota_pis=item_data.get('aliq_pis'),
                        quantidade_bc_pis=item_data.get('quant_bc_pis'),
                        aliquota_pis_reais=item_data.get('aliq_pis_reais'),
                        valor_pis=item_data.get('vl_pis'),
                        cst_cofins=item_data.get('cst_cofins'),
                        valor_bc_cofins=item_data.get('vl_bc_cofins'),
                        aliquota_cofins=item_data.get('aliq_cofins'),
                        quantidade_bc_cofins=item_data.get('quant_bc_cofins'),
                        aliquota_cofins_reais=item_data.get('aliq_cofins_reais'),
                        valor_cofins=item_data.get('vl_cofins'),
                        codigo_conta=item_data.get('cod_cta'),
                        valor_abatimento=item_data.get('vl_abat_nt')
                    )

                    db.session.add(item)
                    itens_processados.append(item)

            except Exception as e:
                logger.error(f"Erro ao processar nota {nota_data.get('num_doc')}: {str(e)}")
                continue

        return notas_processadas, itens_processados

    def _create_error_import_record(self, filename: str, error_msg: str, empresa_data: Dict = None) -> ImportacaoSped:
        """
        Cria um registro de importação com erro
        """
        try:
            importacao = ImportacaoSped(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                usuario_id=self.usuario_id,
                arquivo_nome=filename,
                cnpj_empresa=empresa_data.get('cnpj') if empresa_data else None,
                razao_social_empresa=empresa_data.get('nome') if empresa_data else None,
                data_inicio=empresa_data.get('dt_ini') if empresa_data else None,
                data_fim=empresa_data.get('dt_fim') if empresa_data else None,
                status='erro',
                mensagem=error_msg
            )
            
            db.session.add(importacao)
            db.session.commit()
            return importacao
        except Exception as e:
            logger.error(f"Erro ao criar registro de importação com erro: {str(e)}")
            return None

    def _create_cenarios_automaticos(self, itens_processados: List[ItemNotaEntrada]):
        """
        Cria cenários automaticamente para os itens de nota de entrada processados
        """
        if not itens_processados:
            return

        logger.info(f"Criando cenários automaticamente para {len(itens_processados)} itens")

        # Inicializar o serviço de cenários
        cenario_service = CenarioService(self.empresa_id, self.escritorio_id)

        for item in itens_processados:
            try:
                # Buscar o cliente e produto relacionados
                cliente = Cliente.query.get(item.nota_entrada.cliente_id)
                produto_entrada = ProdutoEntrada.query.get(item.produto_entrada_id)

                if not cliente or not produto_entrada:
                    logger.warning(f"Cliente ou produto não encontrado para item {item.id}")
                    continue

                # Para cenários SPED, vamos usar o cod_part como identificador
                cliente_id = f"sped_{cliente.cod_part}"
                produto_id = f"sped_{produto_entrada.cod_item}"

                # Determinar a direção (sempre entrada para SPED)
                direcao = 'entrada'
                tipo_operacao = '0'  # Entrada

                # Obter NCM e CFOP do item
                ncm = produto_entrada.ncm
                cfop = item.cfop

                # Criar cenários para cada tipo de tributo se houver valor

                # ICMS
                if item.valor_icms and float(item.valor_icms) > 0:
                    icms_data = {
                        'origem': '0',  # Assumir origem nacional por padrão
                        'cst': item.cst_icms,
                        'mod_bc': '0',  # Assumir valor da operação por padrão
                        'p_red_bc': item.p_red_icms if item.p_red_icms else None,
                        'aliquota': item.aliquota_icms,
                        'direcao': direcao,
                        'tipo_operacao': tipo_operacao,
                        'cfop': cfop,
                        'ncm': ncm
                    }
                    cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'icms', icms_data)

                # ICMS-ST
                if item.valor_icms_st and float(item.valor_icms_st) > 0:
                    icms_st_data = {
                        'origem': '0',
                        'cst': item.cst_icms,
                        'mod_bc': '0',
                        'p_red_bc': item.p_red_icms if item.p_red_icms else None,
                        'aliquota': item.aliquota_icms,
                        'icms_st_mod_bc': '4',  # Assumir MVA por padrão
                        'icms_st_aliquota': item.aliquota_st,
                        'icms_st_p_mva': item.p_mva_icms_st if item.p_mva_icms_st else None,
                        'direcao': direcao,
                        'tipo_operacao': tipo_operacao,
                        'cfop': cfop,
                        'ncm': ncm
                    }
                    cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'icms_st', icms_st_data)

                # IPI
                if item.valor_ipi and float(item.valor_ipi) > 0:
                    ipi_data = {
                        'cst': item.cst_ipi,
                        'aliquota': item.aliquota_ipi,
                        'ex': produto_entrada.ex_ipi,
                        'direcao': direcao,
                        'tipo_operacao': tipo_operacao,
                        'cfop': cfop,
                        'ncm': ncm
                    }
                    cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'ipi', ipi_data)

                # PIS
                if item.valor_pis and float(item.valor_pis) > 0:
                    pis_data = {
                        'cst': item.cst_pis,
                        'aliquota': item.aliquota_pis,
                        'p_red_bc': None,  # Será calculado posteriormente
                        'direcao': direcao,
                        'tipo_operacao': tipo_operacao,
                        'cfop': cfop,
                        'ncm': ncm
                    }
                    cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'pis', pis_data)

                # COFINS
                if item.valor_cofins and float(item.valor_cofins) > 0:
                    cofins_data = {
                        'cst': item.cst_cofins,
                        'aliquota': item.aliquota_cofins,
                        'p_red_bc': item.p_red_cofins if item.p_red_cofins else None,
                        'direcao': direcao,
                        'tipo_operacao': tipo_operacao,
                        'cfop': cfop,
                        'ncm': ncm
                    }
                    cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'cofins', cofins_data)

                logger.info(f"Cenários criados para item {item.id} - Produto: {produto_entrada.cod_item}")

            except Exception as e:
                logger.error(f"Erro ao criar cenários para item {item.id}: {str(e)}")
                continue

        logger.info("Criação automática de cenários concluída")

    def _send_progress(self, message: str, percentage: int):
        """
        Envia notificação de progresso via WebSocket
        """
        if self.websocket_service and self.import_id:
            try:
                self.websocket_service.send_sped_import_progress(self.import_id, {
                    "message": message,
                    "percentage": percentage
                })
            except Exception as e:
                logger.warning(f"Erro ao enviar progresso via WebSocket: {str(e)}")
