"""
Service para importação de arquivos SPED
"""

import logging
import traceback
from typing import Dict, List, Optional
from datetime import datetime

from models import (
    db, Empresa, ClienteEntrada, ProdutoEntrada, NotaEntrada, 
    ItemNotaEntrada, ImportacaoSped
)
from utils.sped_processor import SPEDProcessor

logger = logging.getLogger(__name__)


class SPEDImportService:
    """
    Service para importar arquivos SPED e processar notas de entrada
    """

    def __init__(self, empresa_id: int, escritorio_id: int, usuario_id: int):
        """
        Inicializa o service de importação SPED
        
        Args:
            empresa_id (int): ID da empresa
            escritorio_id (int): ID do escritório
            usuario_id (int): ID do usuário
        """
        self.empresa_id = empresa_id
        self.escritorio_id = escritorio_id
        self.usuario_id = usuario_id

    def import_sped(self, sped_content: str, filename: str) -> Dict:
        """
        Importa um arquivo SPED
        
        Args:
            sped_content (str): Conteúdo do arquivo SPED
            filename (str): Nome do arquivo
            
        Returns:
            dict: Resultado da importação
        """
        importacao = None
        try:
            # Processar o arquivo SPED
            processor = SPEDProcessor(sped_content)
            
            # Obter dados da empresa do SPED
            empresa_data = processor.get_empresa_data()
            
            # Verificar se a empresa existe e se o CNPJ confere
            empresa = Empresa.query.get(self.empresa_id)
            if not empresa:
                importacao = self._create_error_import_record(
                    filename, 'Empresa não encontrada', empresa_data
                )
                return {
                    'success': False,
                    'message': 'Empresa não encontrada',
                    'importacao': importacao.to_dict() if importacao else None
                }

            # Verificar se o CNPJ da empresa confere com o do SPED
            cnpj_sped = empresa_data.get('cnpj', '')
            if cnpj_sped and empresa.cnpj != cnpj_sped:
                importacao = self._create_error_import_record(
                    filename, 
                    f'CNPJ da empresa ({empresa.cnpj}) não confere com o CNPJ do SPED ({cnpj_sped})',
                    empresa_data
                )
                return {
                    'success': False,
                    'message': f'CNPJ da empresa não confere com o CNPJ do SPED',
                    'importacao': importacao.to_dict() if importacao else None
                }

            # Processar participantes (fornecedores)
            participantes = processor.get_participantes_entrada()
            clientes_processados = self._process_clientes(participantes)

            # Processar produtos
            produtos = processor.get_produtos_entrada()
            produtos_processados = self._process_produtos(produtos)

            # Processar notas de entrada
            notas_e_itens = processor.get_all_itens_entrada()
            notas_processadas, itens_processados = self._process_notas_entrada(
                notas_e_itens, clientes_processados, produtos_processados
            )

            # Criar registro de importação com sucesso
            importacao = ImportacaoSped(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                usuario_id=self.usuario_id,
                arquivo_nome=filename,
                cnpj_empresa=empresa_data.get('cnpj'),
                razao_social_empresa=empresa_data.get('nome'),
                data_inicio=empresa_data.get('dt_ini'),
                data_fim=empresa_data.get('dt_fim'),
                total_notas=len(notas_processadas),
                total_itens=len(itens_processados),
                total_clientes=len(clientes_processados),
                total_produtos=len(produtos_processados),
                status='concluido'
            )

            db.session.add(importacao)
            db.session.commit()

            return {
                'success': True,
                'message': 'SPED importado com sucesso',
                'importacao': importacao.to_dict(),
                'totais': {
                    'notas': len(notas_processadas),
                    'itens': len(itens_processados),
                    'clientes': len(clientes_processados),
                    'produtos': len(produtos_processados)
                }
            }

        except Exception as e:
            db.session.rollback()
            error_msg = str(e)
            stack_trace = traceback.format_exc()

            logger.error(f"Erro ao importar SPED {filename}: {error_msg}")
            logger.error(f"Stack trace: {stack_trace}")

            # Criar registro de importação com erro
            if not importacao:
                importacao = self._create_error_import_record(filename, error_msg)

            return {
                'success': False,
                'message': f'Erro ao importar SPED: {error_msg}',
                'error': error_msg,
                'stack_trace': stack_trace,
                'importacao': importacao.to_dict() if importacao else None
            }

    def _process_clientes(self, participantes: Dict) -> List[ClienteEntrada]:
        """
        Processa e salva os clientes (fornecedores) de entrada
        """
        clientes_processados = []
        
        for cod_part, dados in participantes.items():
            try:
                # Verificar se o cliente já existe
                cliente_existente = ClienteEntrada.query.filter_by(
                    empresa_id=self.empresa_id,
                    cod_part=cod_part
                ).first()

                if cliente_existente:
                    clientes_processados.append(cliente_existente)
                    continue

                # Criar novo cliente
                cliente = ClienteEntrada(
                    empresa_id=self.empresa_id,
                    escritorio_id=self.escritorio_id,
                    cod_part=cod_part,
                    cnpj=dados.get('cnpj'),
                    cpf=dados.get('cpf'),
                    razao_social=dados.get('nome', ''),
                    inscricao_estadual=dados.get('ie'),
                    codigo_municipio=dados.get('cod_mun'),
                    suframa=dados.get('suframa'),
                    endereco=dados.get('endereco'),
                    numero=dados.get('numero'),
                    complemento=dados.get('complemento'),
                    bairro=dados.get('bairro'),
                    codigo_pais=dados.get('cod_pais', '1058')
                )

                db.session.add(cliente)
                clientes_processados.append(cliente)

            except Exception as e:
                logger.error(f"Erro ao processar cliente {cod_part}: {str(e)}")
                continue

        return clientes_processados

    def _process_produtos(self, produtos: Dict) -> List[ProdutoEntrada]:
        """
        Processa e salva os produtos de entrada
        """
        produtos_processados = []
        
        for cod_item, dados in produtos.items():
            try:
                # Verificar se o produto já existe
                produto_existente = ProdutoEntrada.query.filter_by(
                    empresa_id=self.empresa_id,
                    cod_item=cod_item
                ).first()

                if produto_existente:
                    produtos_processados.append(produto_existente)
                    continue

                # Criar novo produto
                produto = ProdutoEntrada(
                    empresa_id=self.empresa_id,
                    escritorio_id=self.escritorio_id,
                    cod_item=cod_item,
                    descricao=dados.get('descr_item', ''),
                    codigo_barra=dados.get('cod_barra'),
                    codigo_anterior=dados.get('cod_ant_item'),
                    unidade=dados.get('unid_inv'),
                    tipo_item=dados.get('tipo_item'),
                    ncm=dados.get('cod_ncm'),
                    ex_ipi=dados.get('ex_ipi'),
                    codigo_genero=dados.get('cod_gen'),
                    codigo_lst=dados.get('cod_lst'),
                    aliquota_icms=dados.get('aliq_icms'),
                    cest=dados.get('cest')
                )

                db.session.add(produto)
                produtos_processados.append(produto)

            except Exception as e:
                logger.error(f"Erro ao processar produto {cod_item}: {str(e)}")
                continue

        return produtos_processados

    def _process_notas_entrada(self, notas_e_itens: List, clientes_map: List, produtos_map: List) -> tuple:
        """
        Processa e salva as notas de entrada e seus itens
        """
        notas_processadas = []
        itens_processados = []
        
        # Criar mapas para busca rápida
        clientes_dict = {c.cod_part: c for c in clientes_map}
        produtos_dict = {p.cod_item: p for p in produtos_map}
        
        for nota_data, itens_data in notas_e_itens:
            try:
                # Verificar se a nota já existe
                chave_nf = nota_data.get('chv_nfe')
                if chave_nf:
                    nota_existente = NotaEntrada.query.filter_by(
                        empresa_id=self.empresa_id,
                        chave_nf=chave_nf
                    ).first()
                    
                    if nota_existente:
                        continue

                # Buscar cliente
                cod_part = nota_data.get('cod_part')
                cliente = clientes_dict.get(cod_part)
                if not cliente:
                    logger.warning(f"Cliente {cod_part} não encontrado para nota {nota_data.get('num_doc')}")
                    continue

                # Criar nota de entrada
                nota = NotaEntrada(
                    empresa_id=self.empresa_id,
                    escritorio_id=self.escritorio_id,
                    cliente_entrada_id=cliente.id,
                    ind_oper=nota_data.get('ind_oper'),
                    ind_emit=nota_data.get('ind_emit'),
                    cod_part=cod_part,
                    cod_mod=nota_data.get('cod_mod'),
                    cod_sit=nota_data.get('cod_sit'),
                    serie=nota_data.get('ser'),
                    numero_nf=nota_data.get('num_doc'),
                    chave_nf=chave_nf,
                    data_documento=nota_data.get('dt_doc'),
                    data_entrada_saida=nota_data.get('dt_e_s'),
                    valor_documento=nota_data.get('vl_doc'),
                    ind_pgto=nota_data.get('ind_pgto'),
                    valor_desconto=nota_data.get('vl_desc'),
                    valor_abatimento=nota_data.get('vl_abat_nt'),
                    valor_mercadorias=nota_data.get('vl_merc'),
                    ind_frt=nota_data.get('ind_frt'),
                    valor_frete=nota_data.get('vl_frt'),
                    valor_seguro=nota_data.get('vl_seg'),
                    valor_outras_despesas=nota_data.get('vl_out_da'),
                    valor_bc_icms=nota_data.get('vl_bc_icms'),
                    valor_icms=nota_data.get('vl_icms'),
                    valor_bc_icms_st=nota_data.get('vl_bc_icms_st'),
                    valor_icms_st=nota_data.get('vl_icms_st'),
                    valor_ipi=nota_data.get('vl_ipi'),
                    valor_pis=nota_data.get('vl_pis'),
                    valor_cofins=nota_data.get('vl_cofins'),
                    valor_pis_st=nota_data.get('vl_pis_st'),
                    valor_cofins_st=nota_data.get('vl_cofins_st')
                )

                db.session.add(nota)
                db.session.flush()  # Para obter o ID da nota
                notas_processadas.append(nota)

                # Processar itens da nota
                for item_data in itens_data:
                    cod_item = item_data.get('cod_item')
                    produto = produtos_dict.get(cod_item)
                    if not produto:
                        logger.warning(f"Produto {cod_item} não encontrado para item da nota {nota.numero_nf}")
                        continue

                    item = ItemNotaEntrada(
                        empresa_id=self.empresa_id,
                        escritorio_id=self.escritorio_id,
                        nota_entrada_id=nota.id,
                        produto_entrada_id=produto.id,
                        num_item=item_data.get('num_item'),
                        cod_item=cod_item,
                        descricao_complementar=item_data.get('descr_compl'),
                        quantidade=item_data.get('qtd'),
                        unidade=item_data.get('unid'),
                        valor_item=item_data.get('vl_item'),
                        valor_desconto=item_data.get('vl_desc'),
                        ind_mov=item_data.get('ind_mov'),
                        cst_icms=item_data.get('cst_icms'),
                        cfop=item_data.get('cfop'),
                        codigo_natureza=item_data.get('cod_nat'),
                        valor_bc_icms=item_data.get('vl_bc_icms'),
                        aliquota_icms=item_data.get('aliq_icms'),
                        valor_icms=item_data.get('vl_icms'),
                        valor_bc_icms_st=item_data.get('vl_bc_icms_st'),
                        aliquota_st=item_data.get('aliq_st'),
                        valor_icms_st=item_data.get('vl_icms_st'),
                        ind_apur=item_data.get('ind_apur'),
                        cst_ipi=item_data.get('cst_ipi'),
                        codigo_enquadramento=item_data.get('cod_enq'),
                        valor_bc_ipi=item_data.get('vl_bc_ipi'),
                        aliquota_ipi=item_data.get('aliq_ipi'),
                        valor_ipi=item_data.get('vl_ipi'),
                        cst_pis=item_data.get('cst_pis'),
                        valor_bc_pis=item_data.get('vl_bc_pis'),
                        aliquota_pis=item_data.get('aliq_pis'),
                        quantidade_bc_pis=item_data.get('quant_bc_pis'),
                        aliquota_pis_reais=item_data.get('aliq_pis_reais'),
                        valor_pis=item_data.get('vl_pis'),
                        cst_cofins=item_data.get('cst_cofins'),
                        valor_bc_cofins=item_data.get('vl_bc_cofins'),
                        aliquota_cofins=item_data.get('aliq_cofins'),
                        quantidade_bc_cofins=item_data.get('quant_bc_cofins'),
                        aliquota_cofins_reais=item_data.get('aliq_cofins_reais'),
                        valor_cofins=item_data.get('vl_cofins'),
                        codigo_conta=item_data.get('cod_cta'),
                        valor_abatimento=item_data.get('vl_abat_nt')
                    )

                    db.session.add(item)
                    itens_processados.append(item)

            except Exception as e:
                logger.error(f"Erro ao processar nota {nota_data.get('num_doc')}: {str(e)}")
                continue

        return notas_processadas, itens_processados

    def _create_error_import_record(self, filename: str, error_msg: str, empresa_data: Dict = None) -> ImportacaoSped:
        """
        Cria um registro de importação com erro
        """
        try:
            importacao = ImportacaoSped(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                usuario_id=self.usuario_id,
                arquivo_nome=filename,
                cnpj_empresa=empresa_data.get('cnpj') if empresa_data else None,
                razao_social_empresa=empresa_data.get('nome') if empresa_data else None,
                data_inicio=empresa_data.get('dt_ini') if empresa_data else None,
                data_fim=empresa_data.get('dt_fim') if empresa_data else None,
                status='erro',
                mensagem=error_msg
            )
            
            db.session.add(importacao)
            db.session.commit()
            return importacao
        except Exception as e:
            logger.error(f"Erro ao criar registro de importação com erro: {str(e)}")
            return None
