from .escritorio import db
from sqlalchemy.sql import func


class ClienteEntrada(db.Model):
    __tablename__ = 'cliente_entrada'
    
    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.<PERSON><PERSON>ger, db.<PERSON><PERSON>('empresa.id'), nullable=False)
    escritorio_id = db.Column(db.In<PERSON>ger, db.ForeignKey('escritorio.id'), nullable=False)
    cod_part = db.Column(db.String(50), nullable=False)  # Código do participante no SPED
    cnpj = db.Column(db.String(18))  # CNPJ do fornecedor
    cpf = db.Column(db.String(14))  # CPF para pessoas físicas
    razao_social = db.Column(db.String(255), nullable=False)
    inscricao_estadual = db.Column(db.String(30))
    codigo_municipio = db.Column(db.String(10))
    suframa = db.Column(db.String(20))
    endereco = db.Column(db.String(255))
    numero = db.Column(db.String(20))
    complemento = db.Column(db.String(255))
    bairro = db.Column(db.String(100))
    codigo_pais = db.Column(db.String(10), default='1058')  # Brasil
    data_cadastro = db.Column(db.DateTime, server_default=func.now())
    
    # Relacionamentos
    notas_entrada = db.relationship('NotaEntrada', backref='cliente_entrada', lazy=True)
    
    # Constraint única
    __table_args__ = (
        db.UniqueConstraint('empresa_id', 'cod_part', name='uq_cliente_entrada_empresa_cod_part'),
    )
    
    def __repr__(self):
        return f"<ClienteEntrada {self.cod_part} - {self.razao_social}>"
    
    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'cod_part': self.cod_part,
            'cnpj': self.cnpj,
            'cpf': self.cpf,
            'razao_social': self.razao_social,
            'inscricao_estadual': self.inscricao_estadual,
            'codigo_municipio': self.codigo_municipio,
            'suframa': self.suframa,
            'endereco': self.endereco,
            'numero': self.numero,
            'complemento': self.complemento,
            'bairro': self.bairro,
            'codigo_pais': self.codigo_pais,
            'data_cadastro': self.data_cadastro.isoformat() if self.data_cadastro else None
        }
