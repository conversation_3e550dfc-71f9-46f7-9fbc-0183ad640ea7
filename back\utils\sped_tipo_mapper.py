"""
Utilitário para mapear tipos de produto SPED
"""

# Dicionário SPED (tipo produto) 
SPED_TIPO_PRODUTO = {
    '00': 'Mercadoria para Revenda',
    '01': '<PERSON><PERSON><PERSON>rima',
    '02': 'Embalagem',
    '03': 'Produto em Processo',
    '04': 'Produto Acabado',
    '05': 'Subproduto',
    '06': 'Produto Intermediário',
    '07': 'Material de uso e consumo',
    '08': 'Ativo Imobilizado',
    '09': 'Serviços',
    '10': 'Outros Insumos',
    '99': 'Outras'
}

def get_tipo_produto_descricao(codigo_tipo):
    """
    Retorna a descrição do tipo de produto SPED baseado no código
    
    Args:
        codigo_tipo (str): Código do tipo de produto (00-99)
        
    Returns:
        str: Descrição do tipo de produto ou 'Não Identificado' se não encontrado
    """
    if not codigo_tipo:
        return 'Não Identificado'
    
    # Garantir que o código tenha 2 dígitos
    codigo_formatado = str(codigo_tipo).zfill(2)
    
    return SPED_TIPO_PRODUTO.get(codigo_formatado, 'Não Identificado')

def get_all_tipos_produto():
    """
    Retorna todos os tipos de produto SPED disponíveis
    
    Returns:
        dict: Dicionário com todos os tipos de produto
    """
    return SPED_TIPO_PRODUTO.copy()
