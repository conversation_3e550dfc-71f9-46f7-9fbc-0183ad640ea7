-- Script para atualizar as tabelas SPED com os novos campos
-- Execute este script no banco de dados para adicionar os novos campos

BEGIN;

-- Adicionar novos campos na tabela cliente (unificada)
ALTER TABLE cliente
ADD COLUMN IF NOT EXISTS cod_part VARCHAR(50),
ADD COLUMN IF NOT EXISTS cpf VARCHAR(14),
ADD COLUMN IF NOT EXISTS codigo_municipio VARCHAR(10),
ADD COLUMN IF NOT EXISTS suframa VARCHAR(20),
ADD COLUMN IF NOT EXISTS complemento VARCHAR(255);

-- Atualizar tabela nota_entrada para usar cliente_id em vez de cliente_entrada_id
ALTER TABLE nota_entrada
DROP COLUMN IF EXISTS cliente_entrada_id,
ADD COLUMN IF NOT EXISTS cliente_id INTEGER REFERENCES cliente(id);

-- Adicionar novos campos na tabela item_nota_entrada
ALTER TABLE item_nota_entrada 
ADD COLUMN IF NOT EXISTS p_red_icms DECIMAL(5,4),
ADD COLUMN IF NOT EXISTS p_red_icms_st DECIMAL(5,4),
ADD COLUMN IF NOT EXISTS p_mva_icms_st DECIMAL(5,4),
ADD COLUMN IF NOT EXISTS p_red_ipi DECIMAL(5,4),
ADD COLUMN IF NOT EXISTS p_red_cofins DECIMAL(5,4),
ADD COLUMN IF NOT EXISTS valor_icms_cenario DECIMAL(15,2),
ADD COLUMN IF NOT EXISTS valor_icms_st_cenario DECIMAL(15,2),
ADD COLUMN IF NOT EXISTS valor_ipi_cenario DECIMAL(15,2),
ADD COLUMN IF NOT EXISTS valor_pis_cenario DECIMAL(15,2),
ADD COLUMN IF NOT EXISTS valor_cofins_cenario DECIMAL(15,2);

-- Comentários nos novos campos da tabela cliente
COMMENT ON COLUMN cliente.cod_part IS 'Código do participante no SPED';
COMMENT ON COLUMN cliente.cpf IS 'CPF para pessoas físicas';
COMMENT ON COLUMN cliente.codigo_municipio IS 'Código do município';
COMMENT ON COLUMN cliente.suframa IS 'Código SUFRAMA';
COMMENT ON COLUMN cliente.complemento IS 'Complemento do endereço';

COMMENT ON COLUMN item_nota_entrada.p_red_icms IS 'Percentual de redução de ICMS';
COMMENT ON COLUMN item_nota_entrada.p_red_icms_st IS 'Percentual de redução de ICMS-ST';
COMMENT ON COLUMN item_nota_entrada.p_mva_icms_st IS 'Percentual de MVA de ICMS-ST';
COMMENT ON COLUMN item_nota_entrada.p_red_ipi IS 'Percentual de redução de IPI';
COMMENT ON COLUMN item_nota_entrada.p_red_cofins IS 'Percentual de redução de COFINS';
COMMENT ON COLUMN item_nota_entrada.valor_icms_cenario IS 'Valor ICMS calculado pelo cenário';
COMMENT ON COLUMN item_nota_entrada.valor_icms_st_cenario IS 'Valor ICMS-ST calculado pelo cenário';
COMMENT ON COLUMN item_nota_entrada.valor_ipi_cenario IS 'Valor IPI calculado pelo cenário';
COMMENT ON COLUMN item_nota_entrada.valor_pis_cenario IS 'Valor PIS calculado pelo cenário';
COMMENT ON COLUMN item_nota_entrada.valor_cofins_cenario IS 'Valor COFINS calculado pelo cenário';

COMMIT;
