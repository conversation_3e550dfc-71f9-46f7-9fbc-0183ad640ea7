"""
Serviço WebSocket para comunicação em tempo real
"""
from flask_socketio import SocketIO, emit, join_room, leave_room
from flask_jwt_extended import decode_token
import logging

logger = logging.getLogger(__name__)

class WebSocketService:
    """
    Serviço para gerenciar conexões WebSocket e enviar atualizações de progresso
    """

    def __init__(self, socketio: SocketIO):
        self.socketio = socketio
        self.active_imports = {}  # Armazena importações ativas por usuário
        self.active_audits = {}   # Armazena auditorias ativas por usuário

    def authenticate_user(self, token):
        """
        Autentica usuário através do token JWT
        """
        try:
            decoded_token = decode_token(token)
            return decoded_token['sub']  # user_id
        except Exception as e:
            logger.error(f"Erro ao autenticar usuário: {str(e)}")
            return None

    def join_import_room(self, user_id, import_id):
        """
        Adiciona usuário à sala de uma importação específica
        """
        room = f"import_{import_id}"
        join_room(room)
        logger.info(f"Usuário {user_id} entrou na sala {room}")
        return room

    def leave_import_room(self, user_id, import_id):
        """
        Remove usuário da sala de uma importação específica
        """
        room = f"import_{import_id}"
        leave_room(room)
        logger.info(f"Usuário {user_id} saiu da sala {room}")

    def send_progress_update(self, import_id, progress_data):
        """
        Envia atualização de progresso para todos os clientes na sala da importação
        """
        try:
            room = f"import_{import_id}"
            logger.info(f"Enviando progresso para sala {room}: {progress_data}")
            self.socketio.emit('import_progress', progress_data, room=room)
            logger.info(f"Progresso enviado para sala {room}")
        except Exception as e:
            logger.error(f"Erro ao enviar progresso: {str(e)}")

    def send_import_complete(self, import_id, result_data):
        """
        Envia notificação de importação concluída
        """
        try:
            room = f"import_{import_id}"
            self.socketio.emit('import_complete', result_data, room=room)
            logger.info(f"Importação {import_id} concluída")
        except Exception as e:
            logger.error(f"Erro ao enviar conclusão: {str(e)}")

    def send_import_error(self, import_id, error_data):
        """
        Envia notificação de erro na importação
        """
        try:
            room = f"import_{import_id}"
            self.socketio.emit('import_error', error_data, room=room)
            logger.error(f"Erro na importação {import_id}: {error_data}")
        except Exception as e:
            logger.error(f"Erro ao enviar erro: {str(e)}")

    def create_progress_callback(self, import_id):
        """
        Cria uma função callback para reportar progresso de importação
        """
        def progress_callback(progress_data):
            self.send_progress_update(import_id, progress_data)

        return progress_callback

    def join_audit_room(self, user_id, audit_id):
        """
        Adiciona usuário à sala de uma auditoria específica
        """
        room = f"audit_{audit_id}"
        join_room(room)
        logger.info(f"Usuário {user_id} entrou na sala de auditoria {room}")
        return room

    def leave_audit_room(self, user_id, audit_id):
        """
        Remove usuário da sala de uma auditoria específica
        """
        room = f"audit_{audit_id}"
        leave_room(room)
        logger.info(f"Usuário {user_id} saiu da sala de auditoria {room}")

    def send_audit_progress(self, audit_id, progress_data):
        """
        Envia atualização de progresso de auditoria para todos os clientes na sala
        """
        try:
            room = f"audit_{audit_id}"
            logger.info(f"Enviando progresso de auditoria para sala {room}: {progress_data}")
            self.socketio.emit('audit_progress', progress_data, room=room)
            logger.info(f"Progresso de auditoria enviado para sala {room}")
        except Exception as e:
            logger.error(f"Erro ao enviar progresso de auditoria: {str(e)}")

    def send_audit_complete(self, audit_id, result_data):
        """
        Envia notificação de conclusão de auditoria
        """
        try:
            room = f"audit_{audit_id}"
            logger.info(f"Auditoria {audit_id} concluída")
            self.socketio.emit('audit_complete', result_data, room=room)
            logger.info(f"Notificação de conclusão enviada para sala {room}")
        except Exception as e:
            logger.error(f"Erro ao enviar conclusão de auditoria: {str(e)}")

    def send_audit_error(self, audit_id, error_data):
        """
        Envia notificação de erro na auditoria
        """
        try:
            room = f"audit_{audit_id}"
            logger.error(f"Erro na auditoria {audit_id}: {error_data}")
            self.socketio.emit('audit_error', error_data, room=room)
            logger.info(f"Notificação de erro enviada para sala {room}")
        except Exception as e:
            logger.error(f"Erro ao enviar notificação de erro: {str(e)}")

    def create_audit_progress_callback(self, audit_id):
        """
        Cria uma função callback para reportar progresso de auditoria
        """
        def audit_progress_callback(progress_data):
            self.send_audit_progress(audit_id, progress_data)

        return audit_progress_callback

    # Métodos específicos para importação SPED
    def send_sped_import_start(self, import_id, start_data):
        """
        Envia notificação de início de importação SPED
        """
        try:
            room = f"import_{import_id}"
            self.socketio.emit('sped_import_start', start_data, room=room)
            logger.info(f"Início de importação SPED enviado para sala {room}")
        except Exception as e:
            logger.error(f"Erro ao enviar início de importação SPED: {str(e)}")

    def send_sped_import_progress(self, import_id, progress_data):
        """
        Envia atualização de progresso de importação SPED
        """
        try:
            room = f"import_{import_id}"
            self.socketio.emit('sped_import_progress', progress_data, room=room)
            logger.info(f"Progresso SPED enviado para sala {room}: {progress_data.get('percentage', 0)}%")
        except Exception as e:
            logger.error(f"Erro ao enviar progresso SPED: {str(e)}")

    def send_sped_import_complete(self, import_id, result_data):
        """
        Envia notificação de conclusão de importação SPED
        """
        try:
            room = f"import_{import_id}"
            self.socketio.emit('sped_import_complete', result_data, room=room)
            logger.info(f"Importação SPED {import_id} concluída")
        except Exception as e:
            logger.error(f"Erro ao enviar conclusão SPED: {str(e)}")

    def send_sped_import_error(self, import_id, error_data):
        """
        Envia notificação de erro na importação SPED
        """
        try:
            room = f"import_{import_id}"
            self.socketio.emit('sped_import_error', error_data, room=room)
            logger.error(f"Erro na importação SPED {import_id}: {error_data}")
        except Exception as e:
            logger.error(f"Erro ao enviar erro SPED: {str(e)}")

# Instância global do serviço WebSocket
websocket_service = None

def init_websocket_service(socketio):
    """
    Inicializa o serviço WebSocket
    """
    global websocket_service
    websocket_service = WebSocketService(socketio)
    return websocket_service

def get_websocket_service():
    """
    Retorna a instância do serviço WebSocket
    """
    return websocket_service
