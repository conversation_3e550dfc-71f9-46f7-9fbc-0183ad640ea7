"""
Processador de arquivos SPED para importação de notas de entrada
"""

import re
from datetime import datetime
from typing import Dict, List, Optional, Tuple


class SPEDProcessor:
    """
    Classe para processar arquivos SPED e extrair informações das TAGs necessárias
    """

    def __init__(self, sped_content: str):
        """
        Inicializa o processador com o conteúdo do arquivo SPED
        
        Args:
            sped_content (str): Conteúdo do arquivo SPED
        """
        self.sped_content = sped_content
        self.lines = sped_content.strip().split('\n')
        
        # Dicionários para armazenar os dados extraídos
        self.empresa_data = {}
        self.participantes = {}  # cod_part -> dados
        self.produtos = {}  # cod_item -> dados
        self.notas = []  # Lista de notas de entrada
        self.itens_nota = {}  # nota_index -> lista de itens
        
        # Processar o arquivo
        self._process_file()

    def _process_file(self):
        """
        Processa o arquivo SPED linha por linha
        """
        current_nota_index = None
        
        for line_num, line in enumerate(self.lines):
            line = line.strip()
            if not line:
                continue
                
            # Dividir a linha pelos separadores |
            fields = line.split('|')
            if len(fields) < 2:
                continue
                
            tag = fields[1]  # O primeiro campo é vazio, o segundo é a TAG
            
            try:
                if tag == '0000':
                    self._process_tag_0000(fields)
                elif tag == '0150':
                    self._process_tag_0150(fields)
                elif tag == '0200':
                    self._process_tag_0200(fields)
                elif tag == 'C100':
                    # Verificar se é nota de entrada (IND_OPER = 0)
                    if len(fields) > 2 and fields[2] == '0':
                        current_nota_index = len(self.notas)
                        self._process_tag_c100(fields)
                        self.itens_nota[current_nota_index] = []
                    else:
                        current_nota_index = None
                elif tag == 'C170' and current_nota_index is not None:
                    self._process_tag_c170(fields, current_nota_index)
            except Exception as e:
                print(f"Erro ao processar linha {line_num + 1}: {str(e)}")
                continue

    def _process_tag_0000(self, fields: List[str]):
        """
        Processa TAG 0000 - Dados da empresa
        """
        if len(fields) < 16:
            return
            
        self.empresa_data = {
            'cod_ver': fields[2],
            'cod_fin': fields[3],
            'dt_ini': self._parse_date(fields[4]),
            'dt_fim': self._parse_date(fields[5]),
            'nome': fields[6],
            'cnpj': self._clean_cnpj(fields[7]),
            'cpf': fields[8],
            'uf': fields[9],
            'ie': fields[10],
            'cod_mun': fields[11],
            'im': fields[12],
            'suframa': fields[13],
            'ind_perfil': fields[14],
            'ind_ativ': fields[15]
        }

    def _process_tag_0150(self, fields: List[str]):
        """
        Processa TAG 0150 - Participantes (fornecedores)
        """
        if len(fields) < 13:
            return
            
        cod_part = fields[2]
        self.participantes[cod_part] = {
            'cod_part': cod_part,
            'nome': fields[3],
            'cod_pais': fields[4],
            'cnpj': self._clean_cnpj(fields[5]),
            'cpf': self._clean_cpf(fields[6]),
            'ie': fields[7],
            'cod_mun': fields[8],
            'suframa': fields[9],
            'endereco': fields[10],
            'numero': fields[11],
            'complemento': fields[12],
            'bairro': fields[13] if len(fields) > 13 else ''
        }

    def _process_tag_0200(self, fields: List[str]):
        """
        Processa TAG 0200 - Produtos
        """
        if len(fields) < 13:
            return
            
        cod_item = fields[2]
        self.produtos[cod_item] = {
            'cod_item': cod_item,
            'descr_item': fields[3],
            'cod_barra': fields[4],
            'cod_ant_item': fields[5],
            'unid_inv': fields[6],
            'tipo_item': fields[7],
            'cod_ncm': fields[8],
            'ex_ipi': fields[9],
            'cod_gen': fields[10],
            'cod_lst': fields[11],
            'aliq_icms': self._parse_decimal(fields[12]),
            'cest': fields[13] if len(fields) > 13 else ''
        }

    def _process_tag_c100(self, fields: List[str]):
        """
        Processa TAG C100 - Notas fiscais de entrada
        """
        if len(fields) < 29:
            return
            
        nota = {
            'ind_oper': fields[2],
            'ind_emit': fields[3],
            'cod_part': fields[4],
            'cod_mod': fields[5],
            'cod_sit': fields[6],
            'ser': fields[7],
            'num_doc': fields[8],
            'chv_nfe': fields[9],
            'dt_doc': self._parse_date(fields[10]),
            'dt_e_s': self._parse_date(fields[11]),
            'vl_doc': self._parse_decimal(fields[12]),
            'ind_pgto': fields[13],
            'vl_desc': self._parse_decimal(fields[14]),
            'vl_abat_nt': self._parse_decimal(fields[15]),
            'vl_merc': self._parse_decimal(fields[16]),
            'ind_frt': fields[17],
            'vl_frt': self._parse_decimal(fields[18]),
            'vl_seg': self._parse_decimal(fields[19]),
            'vl_out_da': self._parse_decimal(fields[20]),
            'vl_bc_icms': self._parse_decimal(fields[21]),
            'vl_icms': self._parse_decimal(fields[22]),
            'vl_bc_icms_st': self._parse_decimal(fields[23]),
            'vl_icms_st': self._parse_decimal(fields[24]),
            'vl_ipi': self._parse_decimal(fields[25]),
            'vl_pis': self._parse_decimal(fields[26]),
            'vl_cofins': self._parse_decimal(fields[27]),
            'vl_pis_st': self._parse_decimal(fields[28]) if len(fields) > 28 else 0,
            'vl_cofins_st': self._parse_decimal(fields[29]) if len(fields) > 29 else 0
        }
        
        self.notas.append(nota)

    def _process_tag_c170(self, fields: List[str], nota_index: int):
        """
        Processa TAG C170 - Itens das notas fiscais
        """
        if len(fields) < 37:
            return
            
        item = {
            'num_item': fields[2],
            'cod_item': fields[3],
            'descr_compl': fields[4],
            'qtd': self._parse_decimal(fields[5]),
            'unid': fields[6],
            'vl_item': self._parse_decimal(fields[7]),
            'vl_desc': self._parse_decimal(fields[8]),
            'ind_mov': fields[9],
            'cst_icms': fields[10],
            'cfop': fields[11],
            'cod_nat': fields[12],
            'vl_bc_icms': self._parse_decimal(fields[13]),
            'aliq_icms': self._parse_decimal(fields[14]),
            'vl_icms': self._parse_decimal(fields[15]),
            'vl_bc_icms_st': self._parse_decimal(fields[16]),
            'aliq_st': self._parse_decimal(fields[17]),
            'vl_icms_st': self._parse_decimal(fields[18]),
            'ind_apur': fields[19],
            'cst_ipi': fields[20],
            'cod_enq': fields[21],
            'vl_bc_ipi': self._parse_decimal(fields[22]),
            'aliq_ipi': self._parse_decimal(fields[23]),
            'vl_ipi': self._parse_decimal(fields[24]),
            'cst_pis': fields[25],
            'vl_bc_pis': self._parse_decimal(fields[26]),
            'aliq_pis': self._parse_decimal(fields[27]),
            'quant_bc_pis': self._parse_decimal(fields[28]),
            'aliq_pis_reais': self._parse_decimal(fields[29]),
            'vl_pis': self._parse_decimal(fields[30]),
            'cst_cofins': fields[31],
            'vl_bc_cofins': self._parse_decimal(fields[32]),
            'aliq_cofins': self._parse_decimal(fields[33]),
            'quant_bc_cofins': self._parse_decimal(fields[34]),
            'aliq_cofins_reais': self._parse_decimal(fields[35]),
            'vl_cofins': self._parse_decimal(fields[36]),
            'cod_cta': fields[37] if len(fields) > 37 else '',
            'vl_abat_nt': self._parse_decimal(fields[38]) if len(fields) > 38 else 0
        }
        
        self.itens_nota[nota_index].append(item)

    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """
        Converte string de data do formato DDMMAAAA para datetime
        """
        if not date_str or len(date_str) != 8:
            return None
            
        try:
            day = int(date_str[:2])
            month = int(date_str[2:4])
            year = int(date_str[4:8])
            return datetime(year, month, day)
        except (ValueError, TypeError):
            return None

    def _parse_decimal(self, value_str: str) -> float:
        """
        Converte string para decimal
        """
        if not value_str:
            return 0.0
            
        try:
            # Remover espaços e substituir vírgula por ponto se necessário
            value_str = value_str.strip().replace(',', '.')
            return float(value_str)
        except (ValueError, TypeError):
            return 0.0

    def _clean_cnpj(self, cnpj: str) -> str:
        """
        Remove caracteres especiais do CNPJ
        """
        if not cnpj:
            return ''
        return re.sub(r'[^\d]', '', cnpj)

    def _clean_cpf(self, cpf: str) -> str:
        """
        Remove caracteres especiais do CPF
        """
        if not cpf:
            return ''
        return re.sub(r'[^\d]', '', cpf)

    def get_empresa_data(self) -> Dict:
        """
        Retorna dados da empresa (TAG 0000)
        """
        return self.empresa_data

    def get_participantes_entrada(self) -> Dict[str, Dict]:
        """
        Retorna apenas os participantes que estão envolvidos em notas de entrada
        """
        # Coletar códigos de participantes das notas de entrada
        cod_parts_entrada = set()
        for nota in self.notas:
            if nota.get('ind_oper') == '0':  # Nota de entrada
                cod_parts_entrada.add(nota.get('cod_part'))
        
        # Filtrar participantes
        participantes_entrada = {}
        for cod_part in cod_parts_entrada:
            if cod_part in self.participantes:
                participantes_entrada[cod_part] = self.participantes[cod_part]
        
        return participantes_entrada

    def get_produtos_entrada(self) -> Dict[str, Dict]:
        """
        Retorna apenas os produtos que estão envolvidos em notas de entrada
        """
        # Coletar códigos de produtos das notas de entrada
        cod_items_entrada = set()
        for nota_index, nota in enumerate(self.notas):
            if nota.get('ind_oper') == '0':  # Nota de entrada
                if nota_index in self.itens_nota:
                    for item in self.itens_nota[nota_index]:
                        cod_items_entrada.add(item.get('cod_item'))
        
        # Filtrar produtos
        produtos_entrada = {}
        for cod_item in cod_items_entrada:
            if cod_item in self.produtos:
                produtos_entrada[cod_item] = self.produtos[cod_item]
        
        return produtos_entrada

    def get_notas_entrada(self) -> List[Dict]:
        """
        Retorna apenas as notas de entrada (IND_OPER = 0)
        """
        return [nota for nota in self.notas if nota.get('ind_oper') == '0']

    def get_itens_nota_entrada(self, nota_index: int) -> List[Dict]:
        """
        Retorna os itens de uma nota de entrada específica
        """
        return self.itens_nota.get(nota_index, [])

    def get_all_itens_entrada(self) -> List[Tuple[Dict, List[Dict]]]:
        """
        Retorna todas as notas de entrada com seus respectivos itens
        """
        resultado = []
        for nota_index, nota in enumerate(self.notas):
            if nota.get('ind_oper') == '0':  # Nota de entrada
                itens = self.get_itens_nota_entrada(nota_index)
                resultado.append((nota, itens))
        
        return resultado
