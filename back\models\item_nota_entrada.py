from .escritorio import db
from sqlalchemy.sql import func


class ItemNotaEntrada(db.Model):
    __tablename__ = 'item_nota_entrada'
    
    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.<PERSON>ger, db.<PERSON><PERSON>('empresa.id'), nullable=False)
    escritorio_id = db.<PERSON>umn(db.Integer, db.<PERSON>ey('escritorio.id'), nullable=False)
    nota_entrada_id = db.Column(db.<PERSON>te<PERSON>, db.<PERSON>ey('nota_entrada.id'), nullable=False)
    produto_entrada_id = db.Column(db.Integer, db.<PERSON>ey('produto_entrada.id'), nullable=False)
    num_item = db.Column(db.Integer, nullable=False)  # Número sequencial do item
    cod_item = db.Column(db.String(50), nullable=False)  # Código do item
    descricao_complementar = db.Column(db.String(255))
    quantidade = db.Column(db.Numeric(15, 4))
    unidade = db.Column(db.String(10))
    valor_item = db.Column(db.Numeric(15, 2))
    valor_desconto = db.Column(db.Numeric(15, 2))
    ind_mov = db.Column(db.String(1))  # Indicador de movimentação física
    cst_icms = db.Column(db.String(3))
    cfop = db.Column(db.String(4))
    codigo_natureza = db.Column(db.String(10))
    valor_bc_icms = db.Column(db.Numeric(15, 2))
    aliquota_icms = db.Column(db.Numeric(5, 2))
    valor_icms = db.Column(db.Numeric(15, 2))
    valor_bc_icms_st = db.Column(db.Numeric(15, 2))
    aliquota_st = db.Column(db.Numeric(5, 2))
    valor_icms_st = db.Column(db.Numeric(15, 2))
    ind_apur = db.Column(db.String(1))  # Indicador de apuração do IPI
    cst_ipi = db.Column(db.String(2))
    codigo_enquadramento = db.Column(db.String(10))
    valor_bc_ipi = db.Column(db.Numeric(15, 2))
    aliquota_ipi = db.Column(db.Numeric(5, 2))
    valor_ipi = db.Column(db.Numeric(15, 2))
    cst_pis = db.Column(db.String(2))
    valor_bc_pis = db.Column(db.Numeric(15, 2))
    aliquota_pis = db.Column(db.Numeric(5, 4))
    quantidade_bc_pis = db.Column(db.Numeric(15, 4))
    aliquota_pis_reais = db.Column(db.Numeric(15, 4))
    valor_pis = db.Column(db.Numeric(15, 2))
    cst_cofins = db.Column(db.String(2))
    valor_bc_cofins = db.Column(db.Numeric(15, 2))
    aliquota_cofins = db.Column(db.Numeric(5, 4))
    quantidade_bc_cofins = db.Column(db.Numeric(15, 4))
    aliquota_cofins_reais = db.Column(db.Numeric(15, 4))
    valor_cofins = db.Column(db.Numeric(15, 2))
    codigo_conta = db.Column(db.String(50))
    valor_abatimento = db.Column(db.Numeric(15, 2))
    data_cadastro = db.Column(db.DateTime, server_default=func.now())
    
    def __repr__(self):
        return f"<ItemNotaEntrada {self.num_item} - {self.cod_item}>"
    
    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'nota_entrada_id': self.nota_entrada_id,
            'produto_entrada_id': self.produto_entrada_id,
            'num_item': self.num_item,
            'cod_item': self.cod_item,
            'descricao_complementar': self.descricao_complementar,
            'quantidade': float(self.quantidade) if self.quantidade else None,
            'unidade': self.unidade,
            'valor_item': float(self.valor_item) if self.valor_item else None,
            'valor_desconto': float(self.valor_desconto) if self.valor_desconto else None,
            'ind_mov': self.ind_mov,
            'cst_icms': self.cst_icms,
            'cfop': self.cfop,
            'codigo_natureza': self.codigo_natureza,
            'valor_bc_icms': float(self.valor_bc_icms) if self.valor_bc_icms else None,
            'aliquota_icms': float(self.aliquota_icms) if self.aliquota_icms else None,
            'valor_icms': float(self.valor_icms) if self.valor_icms else None,
            'valor_bc_icms_st': float(self.valor_bc_icms_st) if self.valor_bc_icms_st else None,
            'aliquota_st': float(self.aliquota_st) if self.aliquota_st else None,
            'valor_icms_st': float(self.valor_icms_st) if self.valor_icms_st else None,
            'ind_apur': self.ind_apur,
            'cst_ipi': self.cst_ipi,
            'codigo_enquadramento': self.codigo_enquadramento,
            'valor_bc_ipi': float(self.valor_bc_ipi) if self.valor_bc_ipi else None,
            'aliquota_ipi': float(self.aliquota_ipi) if self.aliquota_ipi else None,
            'valor_ipi': float(self.valor_ipi) if self.valor_ipi else None,
            'cst_pis': self.cst_pis,
            'valor_bc_pis': float(self.valor_bc_pis) if self.valor_bc_pis else None,
            'aliquota_pis': float(self.aliquota_pis) if self.aliquota_pis else None,
            'quantidade_bc_pis': float(self.quantidade_bc_pis) if self.quantidade_bc_pis else None,
            'aliquota_pis_reais': float(self.aliquota_pis_reais) if self.aliquota_pis_reais else None,
            'valor_pis': float(self.valor_pis) if self.valor_pis else None,
            'cst_cofins': self.cst_cofins,
            'valor_bc_cofins': float(self.valor_bc_cofins) if self.valor_bc_cofins else None,
            'aliquota_cofins': float(self.aliquota_cofins) if self.aliquota_cofins else None,
            'quantidade_bc_cofins': float(self.quantidade_bc_cofins) if self.quantidade_bc_cofins else None,
            'aliquota_cofins_reais': float(self.aliquota_cofins_reais) if self.aliquota_cofins_reais else None,
            'valor_cofins': float(self.valor_cofins) if self.valor_cofins else None,
            'codigo_conta': self.codigo_conta,
            'valor_abatimento': float(self.valor_abatimento) if self.valor_abatimento else None,
            'data_cadastro': self.data_cadastro.isoformat() if self.data_cadastro else None
        }
