from .escritorio import db
from sqlalchemy.sql import func


class NotaEntrada(db.Model):
    __tablename__ = 'nota_entrada'
    
    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('empresa.id'), nullable=False)
    escritorio_id = db.Column(db.In<PERSON>ger, db.<PERSON>('escritorio.id'), nullable=False)
    cliente_id = db.Column(db.Integer, db.<PERSON>ey('cliente.id'), nullable=False)
    ind_oper = db.Column(db.String(1), nullable=False)  # 0=Entrada, 1=Saída
    ind_emit = db.Column(db.String(1), nullable=False)  # 0=Emissão própria, 1=Terceiros
    cod_part = db.Column(db.String(50), nullable=False)  # Código do participante
    cod_mod = db.Column(db.String(2), nullable=False)  # Código do modelo do documento
    cod_sit = db.Column(db.String(2), nullable=False)  # Código da situação do documento
    serie = db.Column(db.String(10))
    numero_nf = db.Column(db.String(20), nullable=False)
    chave_nf = db.Column(db.String(44))
    data_documento = db.Column(db.Date, nullable=False)
    data_entrada_saida = db.Column(db.Date, nullable=False)
    valor_documento = db.Column(db.Numeric(15, 2))
    ind_pgto = db.Column(db.String(1))  # Indicador do tipo de pagamento
    valor_desconto = db.Column(db.Numeric(15, 2))
    valor_abatimento = db.Column(db.Numeric(15, 2))
    valor_mercadorias = db.Column(db.Numeric(15, 2))
    ind_frt = db.Column(db.String(1))  # Indicador do tipo de frete
    valor_frete = db.Column(db.Numeric(15, 2))
    valor_seguro = db.Column(db.Numeric(15, 2))
    valor_outras_despesas = db.Column(db.Numeric(15, 2))
    valor_bc_icms = db.Column(db.Numeric(15, 2))
    valor_icms = db.Column(db.Numeric(15, 2))
    valor_bc_icms_st = db.Column(db.Numeric(15, 2))
    valor_icms_st = db.Column(db.Numeric(15, 2))
    valor_ipi = db.Column(db.Numeric(15, 2))
    valor_pis = db.Column(db.Numeric(15, 2))
    valor_cofins = db.Column(db.Numeric(15, 2))
    valor_pis_st = db.Column(db.Numeric(15, 2))
    valor_cofins_st = db.Column(db.Numeric(15, 2))
    data_cadastro = db.Column(db.DateTime, server_default=func.now())
    
    # Relacionamentos
    itens = db.relationship('ItemNotaEntrada', backref='nota_entrada', lazy=True, cascade='all, delete-orphan')
    
    # Constraint única
    __table_args__ = (
        db.UniqueConstraint('empresa_id', 'chave_nf', name='uq_nota_entrada_empresa_chave'),
    )
    
    def __repr__(self):
        return f"<NotaEntrada {self.numero_nf} - {self.chave_nf}>"
    
    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'cliente_entrada_id': self.cliente_entrada_id,
            'ind_oper': self.ind_oper,
            'ind_emit': self.ind_emit,
            'cod_part': self.cod_part,
            'cod_mod': self.cod_mod,
            'cod_sit': self.cod_sit,
            'serie': self.serie,
            'numero_nf': self.numero_nf,
            'chave_nf': self.chave_nf,
            'data_documento': self.data_documento.isoformat() if self.data_documento else None,
            'data_entrada_saida': self.data_entrada_saida.isoformat() if self.data_entrada_saida else None,
            'valor_documento': float(self.valor_documento) if self.valor_documento else None,
            'ind_pgto': self.ind_pgto,
            'valor_desconto': float(self.valor_desconto) if self.valor_desconto else None,
            'valor_abatimento': float(self.valor_abatimento) if self.valor_abatimento else None,
            'valor_mercadorias': float(self.valor_mercadorias) if self.valor_mercadorias else None,
            'ind_frt': self.ind_frt,
            'valor_frete': float(self.valor_frete) if self.valor_frete else None,
            'valor_seguro': float(self.valor_seguro) if self.valor_seguro else None,
            'valor_outras_despesas': float(self.valor_outras_despesas) if self.valor_outras_despesas else None,
            'valor_bc_icms': float(self.valor_bc_icms) if self.valor_bc_icms else None,
            'valor_icms': float(self.valor_icms) if self.valor_icms else None,
            'valor_bc_icms_st': float(self.valor_bc_icms_st) if self.valor_bc_icms_st else None,
            'valor_icms_st': float(self.valor_icms_st) if self.valor_icms_st else None,
            'valor_ipi': float(self.valor_ipi) if self.valor_ipi else None,
            'valor_pis': float(self.valor_pis) if self.valor_pis else None,
            'valor_cofins': float(self.valor_cofins) if self.valor_cofins else None,
            'valor_pis_st': float(self.valor_pis_st) if self.valor_pis_st else None,
            'valor_cofins_st': float(self.valor_cofins_st) if self.valor_cofins_st else None,
            'data_cadastro': self.data_cadastro.isoformat() if self.data_cadastro else None
        }
